# use the official Bun image
FROM oven/bun:latest AS base
WORKDIR /usr/src/app

# Install dependencies for puppeteer and ffmpeg
RUN apt-get update && apt-get install -y \
    wget \
    gnupg \
    ca-certificates \
    procps \
    libxss1 \
    libnss3 \
    libatk-bridge2.0-0 \
    libgtk-3-0 \
    libgbm-dev \
    libasound2 \
    ffmpeg \
    && rm -rf /var/lib/apt/lists/*

# install dependencies into temp directory
FROM base AS install
RUN mkdir -p /temp/dev
COPY package.json bun.lock /temp/dev/
RUN cd /temp/dev && bun install --frozen-lockfile

# copy node_modules from temp directory
# then copy all project files into the image
FROM base
COPY --from=install /temp/dev/node_modules node_modules
COPY . .

# Create directories for VODs
RUN mkdir -p /data/vods /data/vods/live

# run the app in development mode
EXPOSE 3030
CMD ["bun", "dev"]