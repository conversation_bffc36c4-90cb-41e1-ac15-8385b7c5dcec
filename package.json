{"name": "archive", "description": "", "version": "0.0.0", "homepage": "", "private": true, "main": "src", "keywords": ["feathers"], "author": {"name": "TimIsOverpowered"}, "contributors": [], "bugs": {}, "directories": {"lib": "src", "test": "test/", "config": "config/"}, "engines": {"node": "^16.0.0", "npm": ">= 3.0.0"}, "scripts": {"test": "npm run lint && npm run mocha", "lint": "eslint src/. test/. --config .eslintrc.json --fix", "dev": "nodemon src/", "start": "node src/", "mocha": "mocha test/ --recursive --exit", "production": "NODE_ENV=production node src", "production-windows": "set NODE_ENV=production&&node src"}, "standard": {"env": ["mocha"], "ignore": []}, "dependencies": {"@feathersjs/configuration": "^5.0.30", "@feathersjs/errors": "^5.0.30", "@feathersjs/express": "^5.0.30", "@feathersjs/feathers": "^5.0.30", "@feathersjs/transport-commons": "^5.0.30", "compression": "^1.7.4", "cors": "^2.8.5", "dayjs": "^1.11.13", "feathers-hooks-common": "^8.2.1", "feathers-sequelize": "^7.0.3", "fluent-ffmpeg": "^2.1.3", "googleapis": "^144.0.0", "helmet": "^7.1.0", "hls-parser": "^0.13.3", "ioredis": "^5.4.1", "pg": "^8.12.0", "puppeteer": "^23.3.0", "puppeteer-extra": "^3.3.6", "puppeteer-extra-plugin-click-and-wait": "^2.3.3", "puppeteer-extra-plugin-stealth": "^2.11.2", "puppeteer-real-browser": "^1.3.7", "qs": "^6.13.0", "rate-limiter-flexible": "^5.0.3", "redis": "^4.7.0", "selenium-webdriver": "^4.24.0", "sequelize": "^6.37.3", "serve-favicon": "^2.5.0", "winston": "^3.14.2"}, "devDependencies": {"axios": "^1.7.7", "eslint": "^9.9.1", "mocha": "^10.7.3", "nodemon": "^3.1.4"}}