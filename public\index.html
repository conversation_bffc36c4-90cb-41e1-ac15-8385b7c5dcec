<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>VOD Archive Config Editor</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background-color: #f8f9fa;
        }
        .config-section {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
            padding: 20px;
        }
        .section-title {
            color: #495057;
            border-bottom: 2px solid #e9ecef;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        .save-indicator {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1000;
        }
        .array-item {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
        }
        .array-item input {
            flex: 1;
            margin-right: 10px;
        }
        .btn-remove {
            background: #dc3545;
            border: none;
            color: white;
            padding: 5px 10px;
            border-radius: 4px;
            cursor: pointer;
        }
        .btn-add {
            background: #28a745;
            border: none;
            color: white;
            padding: 5px 15px;
            border-radius: 4px;
            cursor: pointer;
            margin-top: 10px;
        }
        .playlist-item, .template-item {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
            gap: 10px;
        }
        .playlist-item input, .template-item input {
            flex: 1;
        }
    </style>
</head>
<body>
    <div class="container-fluid py-4">
        <div class="row">
            <div class="col-12">
                <h1 class="mb-4">
                    <i class="fas fa-cog"></i> VOD Archive Configuration
                </h1>

                <div class="save-indicator">
                    <div id="saveStatus" class="alert alert-info d-none">
                        <i class="fas fa-spinner fa-spin"></i> Saving...
                    </div>
                </div>

                <!-- General Settings -->
                <div class="config-section">
                    <h3 class="section-title">
                        <i class="fas fa-user"></i> General Settings
                    </h3>
                    <div class="row">
                        <div class="col-md-6">
                            <label for="channel" class="form-label">Channel Name</label>
                            <input type="text" class="form-control" id="channel" name="channel">
                        </div>
                        <div class="col-md-6">
                            <label for="timezone" class="form-label">Timezone</label>
                            <input type="text" class="form-control" id="timezone" name="timezone">
                        </div>
                    </div>
                </div>

                <!-- Platform Settings -->
                <div class="config-section">
                    <h3 class="section-title">
                        <i class="fas fa-broadcast-tower"></i> Platform Settings
                    </h3>
                    <div class="row">
                        <div class="col-md-4">
                            <h5>Twitch</h5>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="twitchEnabled" name="twitch.enabled">
                                <label class="form-check-label" for="twitchEnabled">
                                    Enabled
                                </label>
                            </div>
                            <label for="twitchUsername" class="form-label">Username</label>
                            <input type="text" class="form-control" id="twitchUsername" name="twitch.username">
                        </div>
                        <div class="col-md-4">
                            <h5>Kick</h5>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="kickEnabled" name="kick.enabled">
                                <label class="form-check-label" for="kickEnabled">
                                    Enabled
                                </label>
                            </div>
                            <label for="kickUsername" class="form-label">Username</label>
                            <input type="text" class="form-control" id="kickUsername" name="kick.username">
                        </div>
                    </div>
                </div>

                <!-- Download Settings -->
                <div class="config-section">
                    <h3 class="section-title">
                        <i class="fas fa-download"></i> Download Settings
                    </h3>
                    <div class="row">
                        <div class="col-md-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="chatDownload" name="chatDownload">
                                <label class="form-check-label" for="chatDownload">
                                    Download Chat
                                </label>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="vodDownload" name="vodDownload">
                                <label class="form-check-label" for="vodDownload">
                                    Download VODs
                                </label>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="saveHLS" name="saveHLS">
                                <label class="form-check-label" for="saveHLS">
                                    Save HLS
                                </label>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="saveMP4" name="saveMP4">
                                <label class="form-check-label" for="saveMP4">
                                    Save MP4
                                </label>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- YouTube Settings -->
                <div class="config-section">
                    <h3 class="section-title">
                        <i class="fab fa-youtube"></i> YouTube Settings
                    </h3>

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="youtubeUpload" name="youtube.upload">
                                <label class="form-check-label" for="youtubeUpload">
                                    Enable Upload
                                </label>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="youtubePublic" name="youtube.public">
                                <label class="form-check-label" for="youtubePublic">
                                    Public Videos
                                </label>
                            </div>
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-4">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="youtubeVodUpload" name="youtube.vodUpload">
                                <label class="form-check-label" for="youtubeVodUpload">
                                    VOD Upload
                                </label>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="youtubePerGameUpload" name="youtube.perGameUpload">
                                <label class="form-check-label" for="youtubePerGameUpload">
                                    Per Game Upload
                                </label>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="youtubeLiveUpload" name="youtube.liveUpload">
                                <label class="form-check-label" for="youtubeLiveUpload">
                                    Live Upload
                                </label>
                            </div>
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="youtubeMultiTrack" name="youtube.multiTrack">
                                <label class="form-check-label" for="youtubeMultiTrack">
                                    Multi Track
                                </label>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <label for="youtubeSplitDuration" class="form-label">Split Duration (seconds)</label>
                            <input type="number" class="form-control" id="youtubeSplitDuration" name="youtube.splitDuration">
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="youtubeDescription" class="form-label">Description Template</label>
                        <textarea class="form-control" id="youtubeDescription" name="youtube.description" rows="4"></textarea>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">Restricted Games</label>
                        <div id="restrictedGamesContainer"></div>
                        <button type="button" class="btn-add" onclick="addRestrictedGame()">
                            <i class="fas fa-plus"></i> Add Game
                        </button>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">Playlists by Chapter</label>
                        <div id="playlistsContainer"></div>
                        <button type="button" class="btn-add" onclick="addPlaylist()">
                            <i class="fas fa-plus"></i> Add Playlist
                        </button>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">Title Templates</label>
                        <div id="titleTemplatesContainer"></div>
                        <button type="button" class="btn-add" onclick="addTitleTemplate()">
                            <i class="fas fa-plus"></i> Add Template
                        </button>
                    </div>
                </div>

                <!-- Save Button -->
                <div class="text-center">
                    <button type="button" class="btn btn-primary btn-lg" onclick="saveConfig()">
                        <i class="fas fa-save"></i> Save Configuration
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="config.js"></script>
</body>
</html>
