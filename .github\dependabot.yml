version: 2
updates:
- package-ecosystem: npm
  directory: "/"
  schedule:
    interval: daily
  open-pull-requests-limit: 10
  ignore:
  - dependency-name: googleapis
    versions:
    - 67.0.0
    - 67.1.0
    - 67.1.1
    - 68.0.0
    - 70.0.0
    - 71.0.0
    - 72.0.0
  - dependency-name: eslint
    versions:
    - 7.18.0
    - 7.19.0
    - 7.20.0
    - 7.21.0
    - 7.22.0
    - 7.23.0
    - 7.24.0
  - dependency-name: redis
    versions:
    - 3.1.1
  - dependency-name: y18n
    versions:
    - 4.0.1
    - 4.0.2
    - 4.0.3
  - dependency-name: hls-parser
    versions:
    - 0.10.0
    - 0.10.1
    - 0.10.2
    - 0.10.3
    - 0.8.0
    - 0.9.0
  - dependency-name: sequelize
    versions:
    - 6.5.0
    - 6.5.1
    - 6.6.1
    - 6.6.2
  - dependency-name: mocha
    versions:
    - 8.3.0
    - 8.3.1
    - 8.3.2
  - dependency-name: express-rate-limit
    versions:
    - 5.2.5
    - 5.2.6
  - dependency-name: feathers-hooks-common
    versions:
    - 5.0.5
  - dependency-name: helmet
    versions:
    - 4.4.1
  - dependency-name: nodemon
    versions:
    - 2.0.7
