const { google } = require('googleapis');
const readline = require('readline');
const fs = require('fs');
const path = require('path');
const config = require('./config/config.json');

const OAuth2 = google.auth.OAuth2;

// Update the redirect URL to be more specific
const redirectUrl = 'http://localhost:5656';

// Create an OAuth2 client
const oauth2Client = new OAuth2(
  config.google.client_id,
  config.google.client_secret,
  redirectUrl  // Use the updated redirect URL
);

// Generate the authorization URL
const authUrl = oauth2Client.generateAuthUrl({
  access_type: 'offline',
  scope: [
    'https://www.googleapis.com/auth/youtube.force-ssl',
    'https://www.googleapis.com/auth/youtube',
    'https://www.googleapis.com/auth/youtube.upload'
  ],
  prompt: 'consent'  // Add this to force consent screen and get refresh token
});

console.log('Authorize this app by visiting this URL:', authUrl);

// Create readline interface for user input
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

// Ask for the authorization code
rl.question('Enter the code from that page here: ', (code) => {
  rl.close();
  
  // Exchange the authorization code for tokens
  oauth2Client.getToken(code, (err, tokens) => {
    if (err) {
      console.error('Error getting tokens:', err);
      return;
    }
    
    console.log('Tokens acquired!');
    
    // Save tokens to config
    config.youtube.auth.access_token = tokens.access_token;
    config.youtube.auth.refresh_token = tokens.refresh_token;
    
    // Write updated config back to file
    fs.writeFile(
      path.resolve(__dirname, './config/config.json'),
      JSON.stringify(config, null, 4),
      (err) => {
        if (err) return console.error(err);
        console.log('Tokens saved to config.json');
      }
    );
  });
});
