const hooks = require('./config.hooks');

module.exports = function (app) {
  const options = {
    paginate: app.get('paginate')
  };

  // Initialize our service with any options it requires
  app.use('/config', new ConfigService(options, app));

  // Get our initialized service so that we can register hooks
  const service = app.service('config');

  service.hooks(hooks);
};

class ConfigService {
  constructor(options, app) {
    this.options = options || {};
    this.app = app;
  }

  async get(id, params) {
    return this.find(params);
  }

  async create(data, params) {
    throw new Error('Creating config is not supported');
  }

  async update(id, data, params) {
    return this.patch(id, data, params);
  }

  async remove(id, params) {
    throw new Error('Removing config is not supported');
  }

  async create(data, params) {
    if (data.action === 'preview-titles') {
      return this.previewTitles(data);
    }
    throw new Error('Creating config is not supported');
  }

  async previewTitles(data) {
    const dayjs = require('dayjs');
    const timezone = require('dayjs/plugin/timezone');
    const utc = require('dayjs/plugin/utc');
    dayjs.extend(utc);
    dayjs.extend(timezone);

    try {
      const fs = require('fs');
      const path = require('path');
      const configPath = path.join(process.cwd(), 'config', 'config.json');
      const configData = JSON.parse(fs.readFileSync(configPath, 'utf8'));

      // Get sample data from database
      const episodesService = this.app.service('episodes');
      const vodsService = this.app.service('vods');
      const gamesService = this.app.service('games');

      // Get recent VODs for sample dates
      const recentVods = await vodsService.find({
        query: {
          $limit: 5,
          $sort: { createdAt: -1 }
        }
      });

      // Get unique game names from episodes and games
      const episodes = await episodesService.find({
        query: { $limit: 10 }
      });

      const games = await gamesService.find({
        query: {
          $limit: 10,
          $select: ['game_name']
        }
      });

      // Collect unique game names
      const gameNames = new Set();
      episodes.data.forEach(ep => gameNames.add(ep.game_name));
      games.data.forEach(game => game.game_name && gameNames.add(game.game_name));

      // Add some common games if database is empty
      if (gameNames.size === 0) {
        ['Minecraft', 'Just Chatting', 'Ghost of Tsushima', 'The Long Dark'].forEach(name => gameNames.add(name));
      }

      const previews = [];
      const templates = data.titleTemplates || {};
      const sampleDate = recentVods.data.length > 0
        ? dayjs(recentVods.data[0].createdAt).tz(configData.timezone).format("DD.MM.YYYY").toUpperCase()
        : dayjs().tz(configData.timezone).format("DD.MM.YYYY").toUpperCase();

      // Preview each template
      for (const [gameName, template] of Object.entries(templates)) {
        if (!template.trim()) continue;

        let episodeCount = 1;
        if (template.includes('${episodeCount}')) {
          const episodeData = episodes.data.find(ep => ep.game_name === gameName);
          episodeCount = episodeData ? episodeData.episode_count + 1 : 1;
        }

        // Generate previews for different scenarios
        const scenarios = [
          { partNumber: '', totalParts: 1, description: 'Single video' },
          { partNumber: ' (Part 1)', totalParts: 3, description: 'Multi-part (Part 1 of 3)' },
          { partNumber: ' (Part 2)', totalParts: 3, description: 'Multi-part (Part 2 of 3)' }
        ];

        scenarios.forEach(scenario => {
          let previewTitle = template
            .replace(/\$\{config\.channel\}/g, configData.channel)
            .replace(/\$\{gameName\}/g, gameName)
            .replace(/\$\{date\}/g, sampleDate)
            .replace(/\$\{episodeCount\}/g, episodeCount)
            .replace(/\$\{partNumber\}/g, scenario.partNumber);

          previews.push({
            gameName,
            template,
            preview: previewTitle,
            scenario: scenario.description,
            variables: {
              channel: configData.channel,
              gameName,
              date: sampleDate,
              episodeCount,
              partNumber: scenario.partNumber
            }
          });
        });
      }

      // Add default template preview if it exists
      const defaultTemplate = templates.default;
      if (defaultTemplate && defaultTemplate.trim()) {
        Array.from(gameNames).slice(0, 3).forEach(gameName => {
          let episodeCount = 1;
          if (defaultTemplate.includes('${episodeCount}')) {
            const episodeData = episodes.data.find(ep => ep.game_name === gameName);
            episodeCount = episodeData ? episodeData.episode_count + 1 : 1;
          }

          let previewTitle = defaultTemplate
            .replace(/\$\{config\.channel\}/g, configData.channel)
            .replace(/\$\{gameName\}/g, gameName)
            .replace(/\$\{date\}/g, sampleDate)
            .replace(/\$\{episodeCount\}/g, episodeCount)
            .replace(/\$\{partNumber\}/g, '');

          previews.push({
            gameName: `${gameName} (using default)`,
            template: defaultTemplate,
            preview: previewTitle,
            scenario: 'Default template',
            variables: {
              channel: configData.channel,
              gameName,
              date: sampleDate,
              episodeCount,
              partNumber: ''
            }
          });
        });
      }

      return {
        previews,
        availableGames: Array.from(gameNames),
        sampleData: {
          channel: configData.channel,
          timezone: configData.timezone,
          sampleDate,
          totalEpisodes: episodes.total,
          totalGames: games.total,
          totalVods: recentVods.total
        }
      };

    } catch (error) {
      throw new Error(`Failed to generate title previews: ${error.message}`);
    }
  }

  async find(params) {
    const fs = require('fs');
    const path = require('path');

    try {
      const configPath = path.join(process.cwd(), 'config', 'config.json');
      const configData = JSON.parse(fs.readFileSync(configPath, 'utf8'));

      // Return only the editable parts
      return {
        channel: configData.channel,
        youtube: {
          description: configData.youtube.description,
          public: configData.youtube.public,
          restrictedGames: configData.youtube.restrictedGames,
          playlistsByChapter: configData.youtube.playlistsByChapter,
          titleTemplates: configData.youtube.titleTemplates,
          splitDuration: configData.youtube.splitDuration,
          liveUpload: configData.youtube.liveUpload,
          multiTrack: configData.youtube.multiTrack,
          upload: configData.youtube.upload,
          vodUpload: configData.youtube.vodUpload,
          perGameUpload: configData.youtube.perGameUpload
        },
        twitch: {
          enabled: configData.twitch.enabled,
          username: configData.twitch.username
        },
        kick: {
          enabled: configData.kick.enabled,
          username: configData.kick.username
        },
        chatDownload: configData.chatDownload,
        vodDownload: configData.vodDownload,
        saveHLS: configData.saveHLS,
        saveMP4: configData.saveMP4,
        timezone: configData.timezone
      };
    } catch (error) {
      throw new Error(`Failed to read config: ${error.message}`);
    }
  }

  async patch(id, data, params) {
    const fs = require('fs');
    const path = require('path');

    try {
      const configPath = path.join(process.cwd(), 'config', 'config.json');
      const configData = JSON.parse(fs.readFileSync(configPath, 'utf8'));

      // Update only the allowed fields
      if (data.channel !== undefined) {
        configData.channel = data.channel;
      }

      if (data.youtube) {
        if (data.youtube.description !== undefined) {
          configData.youtube.description = data.youtube.description;
        }
        if (data.youtube.public !== undefined) {
          configData.youtube.public = data.youtube.public;
        }
        if (data.youtube.restrictedGames !== undefined) {
          configData.youtube.restrictedGames = data.youtube.restrictedGames;
        }
        if (data.youtube.playlistsByChapter !== undefined) {
          configData.youtube.playlistsByChapter = data.youtube.playlistsByChapter;
        }
        if (data.youtube.titleTemplates !== undefined) {
          configData.youtube.titleTemplates = data.youtube.titleTemplates;
        }
        if (data.youtube.splitDuration !== undefined) {
          configData.youtube.splitDuration = data.youtube.splitDuration;
        }
        if (data.youtube.liveUpload !== undefined) {
          configData.youtube.liveUpload = data.youtube.liveUpload;
        }
        if (data.youtube.multiTrack !== undefined) {
          configData.youtube.multiTrack = data.youtube.multiTrack;
        }
        if (data.youtube.upload !== undefined) {
          configData.youtube.upload = data.youtube.upload;
        }
        if (data.youtube.vodUpload !== undefined) {
          configData.youtube.vodUpload = data.youtube.vodUpload;
        }
        if (data.youtube.perGameUpload !== undefined) {
          configData.youtube.perGameUpload = data.youtube.perGameUpload;
        }
      }

      if (data.twitch) {
        if (data.twitch.enabled !== undefined) {
          configData.twitch.enabled = data.twitch.enabled;
        }
        if (data.twitch.username !== undefined) {
          configData.twitch.username = data.twitch.username;
        }
      }

      if (data.kick) {
        if (data.kick.enabled !== undefined) {
          configData.kick.enabled = data.kick.enabled;
        }
        if (data.kick.username !== undefined) {
          configData.kick.username = data.kick.username;
        }
      }

      if (data.chatDownload !== undefined) {
        configData.chatDownload = data.chatDownload;
      }
      if (data.vodDownload !== undefined) {
        configData.vodDownload = data.vodDownload;
      }
      if (data.saveHLS !== undefined) {
        configData.saveHLS = data.saveHLS;
      }
      if (data.saveMP4 !== undefined) {
        configData.saveMP4 = data.saveMP4;
      }
      if (data.timezone !== undefined) {
        configData.timezone = data.timezone;
      }

      // Write back to file
      fs.writeFileSync(configPath, JSON.stringify(configData, null, 4));

      return this.find(params);
    } catch (error) {
      throw new Error(`Failed to update config: ${error.message}`);
    }
  }
}
