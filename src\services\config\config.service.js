const hooks = require('./config.hooks');

module.exports = function (app) {
  const options = {
    paginate: app.get('paginate')
  };

  // Initialize our service with any options it requires
  app.use('/config', new ConfigService(options));

  // Get our initialized service so that we can register hooks
  const service = app.service('config');

  service.hooks(hooks);
};

class ConfigService {
  constructor(options) {
    this.options = options || {};
  }

  async get(id, params) {
    return this.find(params);
  }

  async create(data, params) {
    throw new Error('Creating config is not supported');
  }

  async update(id, data, params) {
    return this.patch(id, data, params);
  }

  async remove(id, params) {
    throw new Error('Removing config is not supported');
  }

  async find(params) {
    const fs = require('fs');
    const path = require('path');

    try {
      const configPath = path.join(process.cwd(), 'config', 'config.json');
      const configData = JSON.parse(fs.readFileSync(configPath, 'utf8'));

      // Return only the editable parts
      return {
        channel: configData.channel,
        youtube: {
          description: configData.youtube.description,
          public: configData.youtube.public,
          restrictedGames: configData.youtube.restrictedGames,
          playlistsByChapter: configData.youtube.playlistsByChapter,
          titleTemplates: configData.youtube.titleTemplates,
          splitDuration: configData.youtube.splitDuration,
          liveUpload: configData.youtube.liveUpload,
          multiTrack: configData.youtube.multiTrack,
          upload: configData.youtube.upload,
          vodUpload: configData.youtube.vodUpload,
          perGameUpload: configData.youtube.perGameUpload
        },
        twitch: {
          enabled: configData.twitch.enabled,
          username: configData.twitch.username
        },
        kick: {
          enabled: configData.kick.enabled,
          username: configData.kick.username
        },
        chatDownload: configData.chatDownload,
        vodDownload: configData.vodDownload,
        saveHLS: configData.saveHLS,
        saveMP4: configData.saveMP4,
        timezone: configData.timezone
      };
    } catch (error) {
      throw new Error(`Failed to read config: ${error.message}`);
    }
  }

  async patch(id, data, params) {
    const fs = require('fs');
    const path = require('path');

    try {
      const configPath = path.join(process.cwd(), 'config', 'config.json');
      const configData = JSON.parse(fs.readFileSync(configPath, 'utf8'));

      // Update only the allowed fields
      if (data.channel !== undefined) {
        configData.channel = data.channel;
      }

      if (data.youtube) {
        if (data.youtube.description !== undefined) {
          configData.youtube.description = data.youtube.description;
        }
        if (data.youtube.public !== undefined) {
          configData.youtube.public = data.youtube.public;
        }
        if (data.youtube.restrictedGames !== undefined) {
          configData.youtube.restrictedGames = data.youtube.restrictedGames;
        }
        if (data.youtube.playlistsByChapter !== undefined) {
          configData.youtube.playlistsByChapter = data.youtube.playlistsByChapter;
        }
        if (data.youtube.titleTemplates !== undefined) {
          configData.youtube.titleTemplates = data.youtube.titleTemplates;
        }
        if (data.youtube.splitDuration !== undefined) {
          configData.youtube.splitDuration = data.youtube.splitDuration;
        }
        if (data.youtube.liveUpload !== undefined) {
          configData.youtube.liveUpload = data.youtube.liveUpload;
        }
        if (data.youtube.multiTrack !== undefined) {
          configData.youtube.multiTrack = data.youtube.multiTrack;
        }
        if (data.youtube.upload !== undefined) {
          configData.youtube.upload = data.youtube.upload;
        }
        if (data.youtube.vodUpload !== undefined) {
          configData.youtube.vodUpload = data.youtube.vodUpload;
        }
        if (data.youtube.perGameUpload !== undefined) {
          configData.youtube.perGameUpload = data.youtube.perGameUpload;
        }
      }

      if (data.twitch) {
        if (data.twitch.enabled !== undefined) {
          configData.twitch.enabled = data.twitch.enabled;
        }
        if (data.twitch.username !== undefined) {
          configData.twitch.username = data.twitch.username;
        }
      }

      if (data.kick) {
        if (data.kick.enabled !== undefined) {
          configData.kick.enabled = data.kick.enabled;
        }
        if (data.kick.username !== undefined) {
          configData.kick.username = data.kick.username;
        }
      }

      if (data.chatDownload !== undefined) {
        configData.chatDownload = data.chatDownload;
      }
      if (data.vodDownload !== undefined) {
        configData.vodDownload = data.vodDownload;
      }
      if (data.saveHLS !== undefined) {
        configData.saveHLS = data.saveHLS;
      }
      if (data.saveMP4 !== undefined) {
        configData.saveMP4 = data.saveMP4;
      }
      if (data.timezone !== undefined) {
        configData.timezone = data.timezone;
      }

      // Write back to file
      fs.writeFileSync(configPath, JSON.stringify(configData, null, 4));

      return this.find(params);
    } catch (error) {
      throw new Error(`Failed to update config: ${error.message}`);
    }
  }
}
