let currentConfig = {};

// Load configuration on page load
document.addEventListener('DOMContentLoaded', function() {
    loadConfig();
});

async function loadConfig() {
    try {
        const response = await fetch('/config');
        const config = await response.json();
        currentConfig = config;
        populateForm(config);
    } catch (error) {
        console.error('Failed to load config:', error);
        showAlert('Failed to load configuration', 'danger');
    }
}

function populateForm(config) {
    // General settings
    document.getElementById('channel').value = config.channel || '';
    document.getElementById('timezone').value = config.timezone || '';

    // Platform settings
    document.getElementById('twitchEnabled').checked = config.twitch?.enabled || false;
    document.getElementById('twitchUsername').value = config.twitch?.username || '';
    document.getElementById('kickEnabled').checked = config.kick?.enabled || false;
    document.getElementById('kickUsername').value = config.kick?.username || '';

    // Download settings
    document.getElementById('chatDownload').checked = config.chatDownload || false;
    document.getElementById('vodDownload').checked = config.vodDownload || false;
    document.getElementById('saveHLS').checked = config.saveHLS || false;
    document.getElementById('saveMP4').checked = config.saveMP4 || false;

    // YouTube settings
    document.getElementById('youtubeUpload').checked = config.youtube?.upload || false;
    document.getElementById('youtubePublic').checked = config.youtube?.public || false;
    document.getElementById('youtubeVodUpload').checked = config.youtube?.vodUpload || false;
    document.getElementById('youtubePerGameUpload').checked = config.youtube?.perGameUpload || false;
    document.getElementById('youtubeLiveUpload').checked = config.youtube?.liveUpload || false;
    document.getElementById('youtubeMultiTrack').checked = config.youtube?.multiTrack || false;
    document.getElementById('youtubeSplitDuration').value = config.youtube?.splitDuration || 43199;
    document.getElementById('youtubeDescription').value = config.youtube?.description || '';

    // Restricted games
    populateRestrictedGames(config.youtube?.restrictedGames || []);

    // Playlists
    populatePlaylists(config.youtube?.playlistsByChapter || {});

    // Title templates
    populateTitleTemplates(config.youtube?.titleTemplates || {});
}

function populateRestrictedGames(games) {
    const container = document.getElementById('restrictedGamesContainer');
    container.innerHTML = '';

    games.forEach((game, index) => {
        addRestrictedGameElement(game, index);
    });

    if (games.length === 0) {
        addRestrictedGameElement('', 0);
    }
}

function addRestrictedGame() {
    const container = document.getElementById('restrictedGamesContainer');
    const index = container.children.length;
    addRestrictedGameElement('', index);
}

function addRestrictedGameElement(value, index) {
    const container = document.getElementById('restrictedGamesContainer');
    const div = document.createElement('div');
    div.className = 'array-item';
    div.innerHTML = `
        <input type="text" class="form-control" value="${value}" placeholder="Game name"
               onchange="updateRestrictedGame(${index}, this.value)">
        <button type="button" class="btn-remove" onclick="removeRestrictedGame(${index})">
            <i class="fas fa-trash"></i>
        </button>
    `;
    container.appendChild(div);
}

function updateRestrictedGame(index, value) {
    // This will be handled when saving
}

function removeRestrictedGame(index) {
    const container = document.getElementById('restrictedGamesContainer');
    const items = container.children;
    if (items.length > 1) {
        items[index].remove();
        // Re-index remaining items
        Array.from(items).forEach((item, newIndex) => {
            const input = item.querySelector('input');
            const button = item.querySelector('button');
            input.setAttribute('onchange', `updateRestrictedGame(${newIndex}, this.value)`);
            button.setAttribute('onclick', `removeRestrictedGame(${newIndex})`);
        });
    }
}

function getRestrictedGames() {
    const container = document.getElementById('restrictedGamesContainer');
    const inputs = container.querySelectorAll('input');
    const games = [];

    inputs.forEach(input => {
        if (input.value.trim()) {
            games.push(input.value.trim());
        }
    });

    return games;
}

// Playlist functions
function populatePlaylists(playlists) {
    const container = document.getElementById('playlistsContainer');
    container.innerHTML = '';

    const entries = Object.entries(playlists);
    entries.forEach(([game, playlistId], index) => {
        addPlaylistElement(game, playlistId, index);
    });

    if (entries.length === 0) {
        addPlaylistElement('', '', 0);
    }
}

function addPlaylist() {
    const container = document.getElementById('playlistsContainer');
    const index = container.children.length;
    addPlaylistElement('', '', index);
}

function addPlaylistElement(game, playlistId, index) {
    const container = document.getElementById('playlistsContainer');
    const div = document.createElement('div');
    div.className = 'playlist-item';
    div.innerHTML = `
        <input type="text" class="form-control" value="${game}" placeholder="Game/Chapter name"
               data-type="game" data-index="${index}">
        <input type="text" class="form-control" value="${playlistId}" placeholder="Playlist ID"
               data-type="playlist" data-index="${index}">
        <button type="button" class="btn-remove" onclick="removePlaylist(${index})">
            <i class="fas fa-trash"></i>
        </button>
    `;
    container.appendChild(div);
}

function removePlaylist(index) {
    const container = document.getElementById('playlistsContainer');
    const items = container.children;
    if (items.length > 1) {
        items[index].remove();
        // Re-index remaining items
        Array.from(items).forEach((item, newIndex) => {
            const inputs = item.querySelectorAll('input');
            const button = item.querySelector('button');
            inputs.forEach(input => {
                input.setAttribute('data-index', newIndex);
            });
            button.setAttribute('onclick', `removePlaylist(${newIndex})`);
        });
    }
}

function getPlaylists() {
    const container = document.getElementById('playlistsContainer');
    const items = container.children;
    const playlists = {};

    Array.from(items).forEach(item => {
        const gameInput = item.querySelector('input[data-type="game"]');
        const playlistInput = item.querySelector('input[data-type="playlist"]');

        if (gameInput.value.trim() && playlistInput.value.trim()) {
            playlists[gameInput.value.trim()] = playlistInput.value.trim();
        }
    });

    return playlists;
}

// Title template functions
function populateTitleTemplates(templates) {
    const container = document.getElementById('titleTemplatesContainer');
    container.innerHTML = '';

    const entries = Object.entries(templates);
    entries.forEach(([game, template], index) => {
        addTitleTemplateElement(game, template, index);
    });

    if (entries.length === 0) {
        addTitleTemplateElement('', '', 0);
    }
}

function addTitleTemplate() {
    const container = document.getElementById('titleTemplatesContainer');
    const index = container.children.length;
    addTitleTemplateElement('', '', index);
}

function addTitleTemplateElement(game, template, index) {
    const container = document.getElementById('titleTemplatesContainer');
    const div = document.createElement('div');
    div.className = 'template-item';
    div.innerHTML = `
        <input type="text" class="form-control" value="${game}" placeholder="Game name (or 'default')"
               data-type="game" data-index="${index}">
        <input type="text" class="form-control" value="${template}" placeholder="Title template"
               data-type="template" data-index="${index}">
        <button type="button" class="btn-remove" onclick="removeTitleTemplate(${index})">
            <i class="fas fa-trash"></i>
        </button>
    `;
    container.appendChild(div);
}

function removeTitleTemplate(index) {
    const container = document.getElementById('titleTemplatesContainer');
    const items = container.children;
    if (items.length > 1) {
        items[index].remove();
        // Re-index remaining items
        Array.from(items).forEach((item, newIndex) => {
            const inputs = item.querySelectorAll('input');
            const button = item.querySelector('button');
            inputs.forEach(input => {
                input.setAttribute('data-index', newIndex);
            });
            button.setAttribute('onclick', `removeTitleTemplate(${newIndex})`);
        });
    }
}

function getTitleTemplates() {
    const container = document.getElementById('titleTemplatesContainer');
    const items = container.children;
    const templates = {};

    Array.from(items).forEach(item => {
        const gameInput = item.querySelector('input[data-type="game"]');
        const templateInput = item.querySelector('input[data-type="template"]');

        if (gameInput.value.trim() && templateInput.value.trim()) {
            templates[gameInput.value.trim()] = templateInput.value.trim();
        }
    });

    return templates;
}

async function saveConfig() {
    const saveStatus = document.getElementById('saveStatus');
    saveStatus.classList.remove('d-none', 'alert-success', 'alert-danger');
    saveStatus.classList.add('alert-info');
    saveStatus.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Saving...';

    try {
        const formData = {
            channel: document.getElementById('channel').value,
            timezone: document.getElementById('timezone').value,
            twitch: {
                enabled: document.getElementById('twitchEnabled').checked,
                username: document.getElementById('twitchUsername').value
            },
            kick: {
                enabled: document.getElementById('kickEnabled').checked,
                username: document.getElementById('kickUsername').value
            },
            chatDownload: document.getElementById('chatDownload').checked,
            vodDownload: document.getElementById('vodDownload').checked,
            saveHLS: document.getElementById('saveHLS').checked,
            saveMP4: document.getElementById('saveMP4').checked,
            youtube: {
                upload: document.getElementById('youtubeUpload').checked,
                public: document.getElementById('youtubePublic').checked,
                vodUpload: document.getElementById('youtubeVodUpload').checked,
                perGameUpload: document.getElementById('youtubePerGameUpload').checked,
                liveUpload: document.getElementById('youtubeLiveUpload').checked,
                multiTrack: document.getElementById('youtubeMultiTrack').checked,
                splitDuration: parseInt(document.getElementById('youtubeSplitDuration').value) || 43199,
                description: document.getElementById('youtubeDescription').value,
                restrictedGames: getRestrictedGames(),
                playlistsByChapter: getPlaylists(),
                titleTemplates: getTitleTemplates()
            }
        };

        const response = await fetch('/config', {
            method: 'PATCH',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(formData)
        });

        if (response.ok) {
            const updatedConfig = await response.json();
            currentConfig = updatedConfig;

            saveStatus.classList.remove('alert-info');
            saveStatus.classList.add('alert-success');
            saveStatus.innerHTML = '<i class="fas fa-check"></i> Configuration saved successfully!';

            setTimeout(() => {
                saveStatus.classList.add('d-none');
            }, 3000);
        } else {
            throw new Error('Failed to save configuration');
        }
    } catch (error) {
        console.error('Save error:', error);
        saveStatus.classList.remove('alert-info');
        saveStatus.classList.add('alert-danger');
        saveStatus.innerHTML = '<i class="fas fa-exclamation-triangle"></i> Failed to save configuration';

        setTimeout(() => {
            saveStatus.classList.add('d-none');
        }, 5000);
    }
}

function showAlert(message, type) {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    document.body.insertBefore(alertDiv, document.body.firstChild);

    setTimeout(() => {
        alertDiv.remove();
    }, 5000);
}

async function previewTitles() {
    const modal = new bootstrap.Modal(document.getElementById('titlePreviewModal'));
    modal.show();

    // Reset content to loading state
    document.getElementById('previewContent').innerHTML = `
        <div class="text-center">
            <i class="fas fa-spinner fa-spin fa-2x" style="color: var(--accent-color);"></i>
            <p class="mt-2" style="color: var(--text-secondary);">Loading previews...</p>
        </div>
    `;

    try {
        const titleTemplates = getTitleTemplates();

        const response = await fetch('/config', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                action: 'preview-titles',
                titleTemplates: titleTemplates
            })
        });

        if (response.ok) {
            const data = await response.json();
            displayTitlePreviews(data);
        } else {
            throw new Error('Failed to generate previews');
        }
    } catch (error) {
        console.error('Preview error:', error);
        document.getElementById('previewContent').innerHTML = `
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-triangle"></i>
                Failed to generate title previews: ${error.message}
            </div>
        `;
    }
}

function displayTitlePreviews(data) {
    const content = document.getElementById('previewContent');

    if (!data.previews || data.previews.length === 0) {
        content.innerHTML = `
            <div class="alert alert-info">
                <i class="fas fa-info-circle"></i>
                No title templates configured. Add some templates above to see previews.
            </div>
        `;
        return;
    }

    let html = `
        <div class="mb-4">
            <h6 style="color: var(--text-primary);">
                <i class="fas fa-database"></i> Sample Data from Database
            </h6>
            <div class="preview-variables">
                <span class="variable-tag">Channel: ${data.sampleData.channel}</span>
                <span class="variable-tag">Date: ${data.sampleData.sampleDate}</span>
                <span class="variable-tag">Episodes: ${data.sampleData.totalEpisodes}</span>
                <span class="variable-tag">Games: ${data.sampleData.totalGames}</span>
                <span class="variable-tag">VODs: ${data.sampleData.totalVods}</span>
            </div>
        </div>
    `;

    // Group previews by game name
    const groupedPreviews = {};
    data.previews.forEach(preview => {
        const key = preview.gameName;
        if (!groupedPreviews[key]) {
            groupedPreviews[key] = [];
        }
        groupedPreviews[key].push(preview);
    });

    Object.entries(groupedPreviews).forEach(([gameName, previews]) => {
        html += `
            <div class="preview-card">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h6 class="game-name mb-0">${gameName}</h6>
                </div>

                <div class="preview-template">
                    Template: ${previews[0].template}
                </div>
        `;

        previews.forEach(preview => {
            html += `
                <div class="mb-3">
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <span class="scenario-badge">${preview.scenario}</span>
                        <div class="preview-variables">
                            ${Object.entries(preview.variables).map(([key, value]) =>
                                `<span class="variable-tag">${key}: ${value}</span>`
                            ).join('')}
                        </div>
                    </div>
                    <div class="preview-title">
                        ${preview.preview}
                    </div>
                </div>
            `;
        });

        html += `</div>`;
    });

    content.innerHTML = html;
}

async function refreshPreviews() {
    await previewTitles();
}
