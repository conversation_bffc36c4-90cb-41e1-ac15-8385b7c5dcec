# Running the VOD Archive Project with <PERSON><PERSON>

This guide explains how to run the VOD Archive project using <PERSON>er and Docker Compose.

## Prerequisites

- [Docker](https://docs.docker.com/get-docker/)
- [Docker Compose](https://docs.docker.com/compose/install/)

## Configuration

Before running the application, you need to set up the configuration files:

1. Copy the template configuration files:
   ```bash
   cp config/template_config.json config/config.json
   cp config/template_default.json config/default.json
   cp config/template_default.json config/production.json
   ```

2. Edit the configuration files with your specific settings:
   - `config/config.json`: Update with your Twitch, YouTube, and other API credentials
   - `config/default.json`: Update with your admin API key
   - `config/production.json`: Same as default.json (used in production mode)

## Running the Application

1. Build and start the containers:
   ```bash
   docker-compose up -d
   ```

2. To view logs:
   ```bash
   docker-compose logs -f
   ```

3. To stop the application:
   ```bash
   docker-compose down
   ```

## Data Persistence

The application uses Docker volumes for data persistence:

- `postgres_data`: Stores the PostgreSQL database
- `redis_data`: Stores Redis data
- `vod_data`: Stores downloaded VODs

## Accessing the Application

The application will be available at http://localhost:3030

## Troubleshooting

If you encounter issues:

1. Check the logs:
   ```bash
   docker-compose logs -f
   ```

2. Restart the services:
   ```bash
   docker-compose restart
   ```

3. Rebuild the application:
   ```bash
   docker-compose build --no-cache
   docker-compose up -d
   ```

## Notes

- The application runs with Node.js 16 as specified in the project requirements
- PostgreSQL 14 and Redis 7 are used as the database and cache
- VODs are stored in the `/data/vods` directory inside the container, which is mapped to a Docker volume
